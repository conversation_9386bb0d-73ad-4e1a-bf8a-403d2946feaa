import{b as v,_ as G}from"./index-DhjenO2x.js";import{u as W,B as h,h as w,F as X,G as Y,v as M,H as Z,k as N,w as ee,I as te,J as ae,i as se,j as F,K as B}from"./ui-CyA36_hy.js";import{k as A,W as C,Q as b,X as c,r as g,e as D,c as ne,m as l,o as oe,S as s,R as o,K as n,j as x}from"./vendor-RHijBMdK.js";import{A as re}from"./Add-DKKcgvRl.js";import{R as le}from"./Refresh-BNivSboa.js";const ie={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ce=c("path",{d:"M408 480H184a72 72 0 0 1-72-72V184a72 72 0 0 1 72-72h224a72 72 0 0 1 72 72v224a72 72 0 0 1-72 72z",fill:"currentColor"},null,-1),de=c("path",{d:"M160 80h235.88A72.12 72.12 0 0 0 328 32H104a72 72 0 0 0-72 72v224a72.12 72.12 0 0 0 48 67.88V160a80 80 0 0 1 80-80z",fill:"currentColor"},null,-1),ue=[ce,de],pe=A({name:"Copy",render:function(i,f){return b(),C("svg",ie,ue)}}),ve={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},he=c("circle",{cx:"256",cy:"256",r:"64",fill:"currentColor"},null,-1),ge=c("path",{d:"M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96c-42.52 0-84.33 12.15-124.27 36.11c-40.73 24.43-77.63 60.12-109.68 106.07a31.92 31.92 0 0 0-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416c46.71 0 93.81-14.43 136.2-41.72c38.46-24.77 72.72-59.66 99.08-100.92a32.2 32.2 0 0 0-.1-34.76zM256 352a96 96 0 1 1 96-96a96.11 96.11 0 0 1-96 96z",fill:"currentColor"},null,-1),fe=[he,ge],ye=A({name:"Eye",render:function(i,f){return b(),C("svg",ve,fe)}}),we={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},_e=c("path",{d:"M456.69 421.39L362.6 327.3a173.81 173.81 0 0 0 34.84-104.58C397.44 126.38 319.06 48 222.72 48S48 126.38 48 222.72s78.38 174.72 174.72 174.72A173.81 173.81 0 0 0 327.3 362.6l94.09 94.09a25 25 0 0 0 35.3-35.3zM97.92 222.72a124.8 124.8 0 1 1 124.8 124.8a124.95 124.95 0 0 1-124.8-124.8z",fill:"currentColor"},null,-1),me=[_e],xe=A({name:"Search",render:function(i,f){return b(),C("svg",we,me)}}),Ae={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ce=c("path",{d:"M296 64h-80a7.91 7.91 0 0 0-8 8v24h96V72a7.91 7.91 0 0 0-8-8z",fill:"none"},null,-1),be=c("path",{d:"M432 96h-96V72a40 40 0 0 0-40-40h-80a40 40 0 0 0-40 40v24H80a16 16 0 0 0 0 32h17l19 304.92c1.42 26.85 22 47.08 48 47.08h184c26.13 0 46.3-19.78 48-47l19-305h17a16 16 0 0 0 0-32zM192.57 416H192a16 16 0 0 1-16-15.43l-8-224a16 16 0 1 1 32-1.14l8 224A16 16 0 0 1 192.57 416zM272 400a16 16 0 0 1-32 0V176a16 16 0 0 1 32 0zm32-304h-96V72a7.91 7.91 0 0 1 8-8h80a7.91 7.91 0 0 1 8 8zm32 304.57A16 16 0 0 1 320 416h-.58A16 16 0 0 1 304 399.43l8-224a16 16 0 1 1 32 1.14z",fill:"currentColor"},null,-1),ke=[Ce,be],V=A({name:"Trash",render:function(i,f){return b(),C("svg",Ae,ke)}}),k={getActivationCodes:r=>v.get("/activation/list",{params:r}),createActivationCodes:r=>v.post("/activation/generate",r),getActivationCode:r=>v.get(`/activation/${r}`),deleteActivationCode:r=>v.delete(`/activation/${r}`),batchDeleteActivationCodes:r=>v.post("/activation/batch-delete",{ids:r}),resetActivationCode:r=>v.post(`/activation/${r}/expire`),exportActivationCodes:r=>v.get("/activation/export",{params:r,responseType:"blob"}),getActivationCodeStats:()=>v.get("/activation/stats")},ze={class:"activation-codes"},$e={class:"page-header"},Ie={class:"header-right"},Se={class:"filter-row"},Fe=A({__name:"ActivationCodes",setup(r){const i=W(),f=g(!1),z=g(!1),_=g(!1),$=g(""),I=g(null),y=g([]),m=g([]),S=g(null),u=D({count:10,expiry_days:30,description:""}),T={count:[{required:!0,type:"number",message:"请输入创建数量",trigger:["input","blur"]},{type:"number",min:1,max:1e3,message:"数量应在1-1000之间",trigger:["input","blur"]}],expiry_days:[{required:!0,type:"number",message:"请输入有效期天数",trigger:["input","blur"]},{type:"number",min:1,max:365,message:"有效期应在1-365天之间",trigger:["input","blur"]}]},U=[{label:"未使用",value:"unused"},{label:"已使用",value:"used"},{label:"已过期",value:"expired"}],d=D({page:1,pageSize:20,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50,100],showQuickJumper:!0}),R=ne(()=>[{type:"selection"},{title:"激活码",key:"code",width:200,render:e=>l("div",{class:"code-cell"},[l("code",{class:"activation-code"},e.code),l(h,{size:"tiny",quaternary:!0,onClick:()=>L(e.code)},{icon:()=>l(w,null,{default:()=>l(pe)})})])},{title:"状态",key:"status",width:100,render:e=>{const a={active:{label:"活跃",type:"success"},unused:{label:"未使用",type:"success"},used:{label:"已使用",type:"info"},expired:{label:"已过期",type:"error"}}[e.status]||{label:e.status,type:"default"};return l(X,{type:a.type},{default:()=>a.label})}},{title:"创建时间",key:"createdAt",width:180,render:e=>{try{return e.createdAt?new Date(e.createdAt).toLocaleString():"-"}catch(t){return console.warn("Invalid createdAt date:",e.createdAt,t),"-"}}},{title:"使用时间",key:"usedAt",width:180,render:e=>{try{return e.usedAt?new Date(e.usedAt).toLocaleString():"-"}catch(t){return console.warn("Invalid usedAt date:",e.usedAt,t),"-"}}},{title:"过期时间",key:"expiresAt",width:180,render:e=>{try{return e.expiresAt?new Date(e.expiresAt).toLocaleString():"-"}catch(t){return console.warn("Invalid expiresAt date:",e.expiresAt,t),"-"}}},{title:"设备指纹",key:"deviceFingerprint",width:150,render:e=>{const t=e.deviceFingerprint||e.device_fingerprint||"";return t?t.substring(0,12)+"...":"-"}},{title:"操作",key:"actions",width:120,render:e=>l("div",{class:"action-buttons"},[l(h,{size:"small",quaternary:!0,onClick:()=>K(e)},{icon:()=>l(w,null,{default:()=>l(ye)})}),l(Y,{onPositiveClick:()=>q(String(e.id))},{trigger:()=>l(h,{size:"small",quaternary:!0,type:"error"},{icon:()=>l(w,null,{default:()=>l(V)})}),default:()=>"确定删除这个激活码吗？"})])}]),p=async()=>{try{f.value=!0;const e=await k.getActivationCodes({page:d.page,size:d.pageSize,status:I.value||void 0,search:$.value||void 0});if(e.success&&e.data){const t=e.data.Items||e.data.items||[];m.value=t.map(a=>({...a,id:String(a.id),deviceFingerprint:a.deviceFingerprint||a.device_fingerprint||"",createdAt:a.createdAt||a.created_at||"",usedAt:a.usedAt||a.used_at||null,expiresAt:a.expiresAt||a.expires_at||"",macAddress:a.macAddress||a.mac_address||"",batchId:a.batchId||a.batch_id||"",status:a.status==="unused"?"active":a.status})),d.itemCount=e.data.Total||e.data.total||0,console.log("Fetched activation codes:",m.value.length,"items")}else m.value=[],d.itemCount=0,console.warn("API response not successful:",e)}catch(e){console.error("Failed to fetch activation codes:",e),i.error("获取激活码列表失败"),m.value=[],d.itemCount=0}finally{f.value=!1}},P=async()=>{if(S.value)try{await S.value.validate(),z.value=!0,(await k.createActivationCodes(u)).success&&(i.success(`成功创建 ${u.count} 个激活码`),_.value=!1,await p(),Object.assign(u,{count:10,expiry_days:30,description:""}))}catch(e){console.error("Failed to create activation codes:",e),i.error("创建激活码失败")}finally{z.value=!1}},q=async e=>{try{(await k.deleteActivationCode(e)).success&&(i.success("删除成功"),await p())}catch(t){console.error("Failed to delete activation code:",t),i.error("删除失败")}},H=async()=>{if(y.value.length!==0)try{(await k.batchDeleteActivationCodes(y.value)).success&&(i.success(`成功删除 ${y.value.length} 个激活码`),y.value=[],await p())}catch(e){console.error("Failed to batch delete activation codes:",e),i.error("批量删除失败")}},L=async e=>{try{await navigator.clipboard.writeText(e),i.success("已复制到剪贴板")}catch(t){console.error("Failed to copy to clipboard:",t),i.error("复制失败")}},K=e=>{console.log("View details:",e)},j=()=>{d.page=1,p()},E=()=>{d.page=1,p()},Q=()=>{p()},J=e=>{d.page=e,p()},O=e=>{d.pageSize=e,d.page=1,p()};return oe(()=>{p()}),(e,t)=>(b(),C("div",ze,[c("div",$e,[t[10]||(t[10]=c("div",{class:"header-left"},[c("h1",null,"激活码管理"),c("p",null,"管理系统中的所有激活码")],-1)),c("div",Ie,[s(n(h),{type:"primary",onClick:t[0]||(t[0]=a=>_.value=!0)},{icon:o(()=>[s(n(w),null,{default:o(()=>[s(n(re))]),_:1})]),default:o(()=>[t[9]||(t[9]=x(" 创建激活码 "))]),_:1,__:[9]})])]),s(n(M),{class:"filter-card"},{default:o(()=>[c("div",Se,[s(n(N),{value:$.value,"onUpdate:value":t[1]||(t[1]=a=>$.value=a),placeholder:"搜索激活码...",clearable:"",style:{width:"300px"},onInput:j},{prefix:o(()=>[s(n(w),null,{default:o(()=>[s(n(xe))]),_:1})]),_:1},8,["value"]),s(n(ee),{value:I.value,"onUpdate:value":[t[2]||(t[2]=a=>I.value=a),E],placeholder:"状态筛选",options:U,clearable:"",style:{width:"150px"}},null,8,["value"]),s(n(h),{onClick:Q},{icon:o(()=>[s(n(w),null,{default:o(()=>[s(n(le))]),_:1})]),default:o(()=>[t[11]||(t[11]=x(" 刷新 "))]),_:1,__:[11]}),s(n(h),{type:"error",disabled:y.value.length===0,onClick:H},{icon:o(()=>[s(n(w),null,{default:o(()=>[s(n(V))]),_:1})]),default:o(()=>[t[12]||(t[12]=x(" 批量删除 "))]),_:1,__:[12]},8,["disabled"])])]),_:1}),s(n(M),null,{default:o(()=>[s(n(te),{columns:R.value,data:m.value,loading:f.value,pagination:d,"row-key":a=>a.id,"checked-row-keys":y.value,"onUpdate:checkedRowKeys":t[3]||(t[3]=a=>y.value=a),"onUpdate:page":J,"onUpdate:pageSize":O},null,8,["columns","data","loading","pagination","row-key","checked-row-keys"])]),_:1}),s(n(Z),{show:_.value,"onUpdate:show":t[8]||(t[8]=a=>_.value=a),preset:"dialog",title:"创建激活码"},{default:o(()=>[s(n(se),{ref_key:"createFormRef",ref:S,model:u,rules:T,"label-placement":"left","label-width":"auto"},{default:o(()=>[s(n(F),{label:"数量",path:"count"},{default:o(()=>[s(n(B),{value:u.count,"onUpdate:value":t[4]||(t[4]=a=>u.count=a),min:1,max:1e3,placeholder:"请输入创建数量",style:{width:"100%"}},null,8,["value"])]),_:1}),s(n(F),{label:"有效期(天)",path:"expiry_days"},{default:o(()=>[s(n(B),{value:u.expiry_days,"onUpdate:value":t[5]||(t[5]=a=>u.expiry_days=a),min:1,max:365,placeholder:"请输入有效期天数",style:{width:"100%"}},null,8,["value"])]),_:1}),s(n(F),{label:"备注",path:"description"},{default:o(()=>[s(n(N),{value:u.description,"onUpdate:value":t[6]||(t[6]=a=>u.description=a),type:"textarea",placeholder:"可选的备注信息",rows:3},null,8,["value"])]),_:1})]),_:1},8,["model"])]),action:o(()=>[s(n(ae),null,{default:o(()=>[s(n(h),{onClick:t[7]||(t[7]=a=>_.value=!1)},{default:o(()=>t[13]||(t[13]=[x("取消")])),_:1,__:[13]}),s(n(h),{type:"primary",loading:z.value,onClick:P},{default:o(()=>t[14]||(t[14]=[x(" 创建 ")])),_:1,__:[14]},8,["loading"])]),_:1})]),_:1},8,["show"])]))}}),Te=G(Fe,[["__scopeId","data-v-2c958aac"]]);export{Te as default};
