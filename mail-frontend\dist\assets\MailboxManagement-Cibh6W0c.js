import{r as J,_ as re}from"./index-DhjenO2x.js";import{u as ie,H as ge,R as ye,S as et,Q as we,j as W,k as ce,T as tt,l as xe,i as Ce,B as O,J as H,I as de,U as st,V as ot,O as lt,w as le,v as ne,h as F,F as se,K as he,W as nt,X as fe,Y as at,L as rt,Z as it,_ as Ue,$ as X,a0 as Y,a1 as ut,a2 as ct,a3 as dt,a4 as pt,a5 as vt,a6 as ze,a7 as ft}from"./ui-CyA36_hy.js";import{k as V,W as _,Q as d,X as o,a1 as Ae,r as z,e as oe,c as Z,w as ae,O as K,R as s,K as e,S as t,a4 as A,j as h,$ as T,F as $e,a2 as Re,H as Ve,m as D,o as Se,E as _t}from"./vendor-RHijBMdK.js";import{D as gt}from"./SpeedometerOutline-D-Vl8HvQ.js";const mt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},kt=o("circle",{cx:"256",cy:"256",r:"208",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),yt=o("path",{fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32",d:"M108.92 108.92l294.16 294.16"},null,-1),ht=[kt,yt],wt=V({name:"BanOutline",render:function(v,x){return d(),_("svg",mt,ht)}}),xt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},bt=o("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ct=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M352 176L217.6 336L160 272"},null,-1),$t=[bt,Ct],Me=V({name:"CheckmarkCircleOutline",render:function(v,x){return d(),_("svg",xt,$t)}}),St={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Mt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M416 128L192 384l-96-96"},null,-1),Tt=[Mt],Pt=V({name:"CheckmarkOutline",render:function(v,x){return d(),_("svg",St,Tt)}}),Ot={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Lt=o("path",{d:"M448 256c0-106-86-192-192-192S64 150 64 256s86 192 192 192s192-86 192-192z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),zt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 320L192 192"},null,-1),It=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M192 320l128-128"},null,-1),Dt=[Lt,zt,It],Te=V({name:"CloseCircleOutline",render:function(v,x){return d(),_("svg",Ot,Dt)}}),jt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Bt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 368L144 144"},null,-1),Nt=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M368 144L144 368"},null,-1),Ut=[Bt,Nt],At=V({name:"CloseOutline",render:function(v,x){return d(),_("svg",jt,Ut)}}),Rt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Vt=o("path",{d:"M320 367.79h76c55 0 100-29.21 100-83.6s-53-81.47-96-83.6c-8.89-85.06-71-136.8-144-136.8c-69 0-113.44 45.79-128 91.2c-60 5.7-112 43.88-112 106.4s54 106.4 120 106.4h56",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Et=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 255.79l-64-64l-64 64"},null,-1),Ft=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 448.21V207.79"},null,-1),qt=[Vt,Et,Ft],Ee=V({name:"CloudUploadOutline",render:function(v,x){return d(),_("svg",Rt,qt)}}),Jt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ht=o("circle",{cx:"256",cy:"256",r:"192",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Wt=[Ht],Gt=V({name:"EllipseOutline",render:function(v,x){return d(),_("svg",Jt,Wt)}}),Kt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Qt=Ae('<path d="M256 48C141.13 48 48 141.13 48 256s93.13 208 208 208s208-93.13 208-208S370.87 48 256 48z" fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32"></path><path d="M256 48c-58.07 0-112.67 93.13-112.67 208S197.93 464 256 464s112.67-93.13 112.67-208S314.07 48 256 48z" fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32"></path><path d="M117.33 117.33c38.24 27.15 86.38 43.34 138.67 43.34s100.43-16.19 138.67-43.34" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path d="M394.67 394.67c-38.24-27.15-86.38-43.34-138.67-43.34s-100.43 16.19-138.67 43.34" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32" d="M256 48v416"></path><path fill="none" stroke="currentColor" stroke-miterlimit="10" stroke-width="32" d="M464 256H48"></path>',6),Xt=[Qt],Yt=V({name:"GlobeOutline",render:function(v,x){return d(),_("svg",Kt,Xt)}}),Zt={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},es=o("path",{d:"M248 64C146.39 64 64 146.39 64 248s82.39 184 184 184s184-82.39 184-184S349.61 64 248 64z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ts=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M220 220h32v116"},null,-1),ss=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32",d:"M208 340h88"},null,-1),os=o("path",{d:"M248 130a26 26 0 1 0 26 26a26 26 0 0 0-26-26z",fill:"currentColor"},null,-1),ls=[es,ts,ss,os],be=V({name:"InformationCircleOutline",render:function(v,x){return d(),_("svg",Zt,ls)}}),ns={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},as=o("path",{d:"M256 48c-79.5 0-144 61.39-144 137c0 87 96 224.87 131.25 272.49a15.77 15.77 0 0 0 25.5 0C304 409.89 400 272.07 400 185c0-75.61-64.5-137-144-137z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),rs=o("circle",{cx:"256",cy:"192",r:"48",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),is=[as,rs],us=V({name:"LocationOutline",render:function(v,x){return d(),_("svg",ns,is)}}),cs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ds=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M176 96h16v320h-16z"},null,-1),ps=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M320 96h16v320h-16z"},null,-1),vs=[ds,ps],fs=V({name:"PauseOutline",render:function(v,x){return d(),_("svg",cs,vs)}}),_s={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},gs=o("path",{d:"M112 111v290c0 17.44 17 28.52 31 20.16l247.9-148.37c12.12-7.25 12.12-26.33 0-33.58L143 90.84c-14-8.36-31 2.72-31 20.16z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),ms=[gs],Ie=V({name:"PlayOutline",render:function(v,x){return d(),_("svg",_s,ms)}}),ks={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},ys=o("path",{d:"M320 146s24.36-12-64-12a160 160 0 1 0 160 160",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),hs=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 58l80 80l-80 80"},null,-1),ws=[ys,hs],_e=V({name:"RefreshOutline",render:function(v,x){return d(),_("svg",ks,ws)}}),xs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},bs=o("ellipse",{cx:"256",cy:"128",rx:"192",ry:"80",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Cs=o("path",{d:"M448 214c0 44.18-86 80-192 80S64 258.18 64 214",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),$s=o("path",{d:"M448 300c0 44.18-86 80-192 80S64 344.18 64 300",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ss=o("path",{d:"M64 127.24v257.52C64 428.52 150 464 256 464s192-35.48 192-79.24V127.24",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-miterlimit":"10","stroke-width":"32"},null,-1),Ms=[bs,Cs,$s,Ss],De=V({name:"ServerOutline",render:function(v,x){return d(),_("svg",xs,Ms)}}),Ts={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ps=o("rect",{x:"96",y:"96",width:"320",height:"320",rx:"24",ry:"24",fill:"none",stroke:"currentColor","stroke-linejoin":"round","stroke-width":"32"},null,-1),Os=[Ps],je=V({name:"StopOutline",render:function(v,x){return d(),_("svg",Ts,Os)}}),Ls={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},zs=o("path",{d:"M434.67 285.59v-29.8c0-98.73-80.24-178.79-179.2-178.79a179 179 0 0 0-140.14 67.36m-38.53 82v29.8C76.8 355 157 435 256 435a180.45 180.45 0 0 0 140-66.92",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Is=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M32 256l44-44l46 44"},null,-1),Ds=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M480 256l-44 44l-46-44"},null,-1),js=[zs,Is,Ds],Bs=V({name:"SyncOutline",render:function(v,x){return d(),_("svg",Ls,js)}}),Ns={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Us=o("path",{d:"M256 64C150 64 64 150 64 256s86 192 192 192s192-86 192-192S362 64 256 64z",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"},null,-1),As=o("path",{fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32",d:"M256 128v144h96"},null,-1),Rs=[Us,As],Be=V({name:"TimeOutline",render:function(v,x){return d(),_("svg",Ns,Rs)}}),Vs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Es=Ae('<path d="M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="32" d="M80 112h352" fill="currentColor"></path><path d="M192 112V72h0a23.93 23.93 0 0 1 24-24h80a23.93 23.93 0 0 1 24 24h0v40" fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M256 176v224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M184 176l8 224"></path><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="32" d="M328 176l-8 224"></path>',6),Fs=[Es],qs=V({name:"TrashOutline",render:function(v,x){return d(),_("svg",Vs,Fs)}}),Js={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Hs=o("path",{d:"M85.57 446.25h340.86a32 32 0 0 0 28.17-47.17L284.18 82.58c-12.09-22.44-44.27-22.44-56.36 0L57.4 399.08a32 32 0 0 0 28.17 47.17z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Ws=o("path",{d:"M250.26 195.39l5.74 122l5.73-121.95a5.74 5.74 0 0 0-5.79-6h0a5.74 5.74 0 0 0-5.68 5.95z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Gs=o("path",{d:"M256 397.25a20 20 0 1 1 20-20a20 20 0 0 1-20 20z",fill:"currentColor"},null,-1),Ks=[Hs,Ws,Gs],Qs=V({name:"WarningOutline",render:function(v,x){return d(),_("svg",Js,Ks)}}),Xs={xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink",viewBox:"0 0 512 512"},Ys=o("path",{d:"M332.41 310.59a115 115 0 0 0-152.8 0",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),Zs=o("path",{d:"M393.46 249.54a201.26 201.26 0 0 0-274.92 0",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),eo=o("path",{d:"M447.72 182.11a288 288 0 0 0-383.44 0",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"},null,-1),to=o("path",{d:"M256 416a32 32 0 1 1 32-32a32 32 0 0 1-32 32z",fill:"currentColor"},null,-1),so=[Ys,Zs,eo,to],oo=V({name:"WifiOutline",render:function(v,x){return d(),_("svg",Xs,so)}}),U={batchImportAccounts(u){return J.post("/mailbox/batch-import",u)},getAccountsList(u){return J.get("/mailbox/accounts",{params:u})},startVerificationTask(u){return J.post("/mailbox/verify",u)},getBatchOperationStatus(u){return J.get(`/mailbox/batch-operation/${u}`)},controlTask(u){return J.post("/mailbox/task-control",u)},getTaskSchedulerStatus(){return J.get("/mailbox/scheduler-status")},getMailboxStatistics(){return J.get("/mailbox/statistics")},deleteAccount(u){return J.delete(`/mailbox/accounts/${u}`)},batchDeleteAccounts(u){return J.post("/mailbox/batch-delete",{account_ids:u})},toggleAccountStatus(u,v){return J.put(`/mailbox/accounts/${u}/status`,{is_disabled:v})},batchDisableAccounts(u){return J.post("/mailbox/batch-disable",{account_ids:u})},parseBatchImportText(u){const v=u.split(`
`).filter(P=>P.trim()),x=[];for(const P of v){const $=P.trim();if(!$)continue;const b=["----","---","--","	"," "];let r="",c="";for(const p of b)if($.includes(p)){const S=$.split(p);if(S.length>=2){r=S[0].trim(),c=S[1].trim();break}}r&&c&&/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(r)&&x.push({email:r,password:c})}return x},validateEmail(u){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(u)},formatImportPreview(u){const v=[],x=[];return u.forEach(P=>{this.validateEmail(P.email)&&P.password?v.push(P):x.push(`${P.email} - 格式错误`)}),{valid:v,invalid:x,summary:{total:u.length,valid:v.length,invalid:x.length}}},getStatusOptions(){return[{label:"全部状态",value:""},{label:"登录成功",value:"success"},{label:"登录失败",value:"failed"},{label:"未登录",value:"pending"}]},getVerificationStatusOptions(){return[{label:"全部状态",value:""},{label:"未验证",value:"unverified"},{label:"验证成功",value:"verified"},{label:"验证失败",value:"failed"}]},getImportSourceOptions(){return[{label:"全部来源",value:""},{label:"手动添加",value:"manual"},{label:"批量导入",value:"batch_import"},{label:"API导入",value:"api"}]},getProxyConfig(){return J.get("/mailbox/proxy-config")},setProxyConfig(u){return J.post("/mailbox/proxy-config",u)},deleteProxyConfig(){return J.delete("/mailbox/proxy-config")},testProxyConfig(u){return J.post("/mailbox/proxy-config/test",u)},formatStatus(u){return{success:{text:"登录成功",type:"success"},failed:{text:"登录失败",type:"error"},pending:{text:"未登录",type:"warning"},verified:{text:"验证成功",type:"success"},unverified:{text:"未验证",type:"info"},active:{text:"活跃",type:"success"},inactive:{text:"非活跃",type:"warning"},disabled:{text:"已禁用",type:"error"}}[u]||{text:u,type:"info"}}},lo={class:"batch-import-modal"},no={key:0,class:"step-content"},ao={class:"step-actions"},ro={key:1,class:"step-content"},io={key:0,class:"text-red-500"},uo={key:0,class:"mb-4"},co={key:0,class:"text-gray-500 mt-2"},po={key:1,class:"mb-4"},vo={class:"step-actions"},fo={key:2,class:"step-content"},_o={key:0,class:"text-center"},go={key:0},mo={key:1},ko=V({__name:"BatchImportModal",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(u,{emit:v}){const x=u,P=v,$=ie(),b=z(1),r=z("process"),c=z(!1),p=z(!1),S=oe({importText:"",source:"批量导入",tags:[],description:"",autoVerify:!0}),f=z({valid:[],invalid:[],summary:{total:0,valid:0,invalid:0}}),L=z(null),E=Z({get:()=>x.visible,set:a=>P("update:visible",a)}),N={importText:{required:!0,message:"请输入导入数据",trigger:"blur"},source:{required:!0,message:"请输入导入来源",trigger:"blur"}},R=[{title:"序号",key:"index",width:60,render:(a,n)=>n+1},{title:"邮箱地址",key:"email",ellipsis:{tooltip:!0}},{title:"密码",key:"password",width:120,render:a=>"●".repeat(a.password.length)}],j=async()=>{if(!S.importText.trim()){$.error("请输入导入数据");return}c.value=!0;try{const a=U.parseBatchImportText(S.importText);if(f.value=U.formatImportPreview(a),f.value.valid.length===0){$.error("没有解析到有效的账户数据，请检查格式");return}b.value=2}catch(a){$.error("解析数据失败"),console.error(a)}finally{c.value=!1}},m=async()=>{p.value=!0;try{const a={accounts:f.value.valid,source:S.source,tags:S.tags,auto_verify:S.autoVerify,description:S.description},n=await U.batchImportAccounts(a);console.log("导入响应:",JSON.stringify(n));const l=n.data||n;L.value={success:l.success,message:l.message,data:l.data},console.log("导入结果:",JSON.stringify(L.value)),b.value=3,r.value=l.success?"finish":"error",l.success&&P("success",l.data)}catch(a){console.log("导入错误:",JSON.stringify(a)),L.value={success:!1,message:a.message||"导入失败"},b.value=3,r.value="error"}finally{p.value=!1}},I=()=>{$.info("功能开发中...")},G=()=>{g(),E.value=!1},g=()=>{b.value=1,r.value="process",S.importText="",S.source="批量导入",S.tags=[],S.description="",S.autoVerify=!0,f.value={valid:[],invalid:[],summary:{total:0,valid:0,invalid:0}},L.value=null};return ae(E,a=>{a||g()}),(a,n)=>(d(),K(e(ge),{show:E.value,"onUpdate:show":n[9]||(n[9]=l=>E.value=l),preset:"dialog",title:"批量导入邮箱",style:{width:"800px"}},{default:s(()=>[o("div",lo,[t(e(et),{current:b.value,status:r.value},{default:s(()=>[t(e(ye),{title:"输入数据"}),t(e(ye),{title:"预览确认"}),t(e(ye),{title:"导入结果"})]),_:1},8,["current","status"]),b.value===1?(d(),_("div",no,[t(e(we),{type:"info",class:"mb-4"},{header:s(()=>n[10]||(n[10]=[h("导入格式说明")])),default:s(()=>[n[11]||(n[11]=o("div",null,[o("p",null,[h("每行一个邮箱账户，格式："),o("code",null,"邮箱地址----密码")]),o("p",null,[h("支持的分隔符："),o("code",null,"----"),h("、"),o("code",null,"---"),h("、"),o("code",null,"--"),h("、制表符、空格")]),o("p",null,"示例："),o("pre",null,`<EMAIL>----password123
<EMAIL>----password456`)],-1))]),_:1,__:[11]}),t(e(Ce),{ref:"formRef",model:S,rules:N},{default:s(()=>[t(e(W),{label:"导入数据",path:"importText"},{default:s(()=>[t(e(ce),{value:S.importText,"onUpdate:value":n[0]||(n[0]=l=>S.importText=l),type:"textarea",placeholder:"请输入邮箱账户数据，每行一个...",rows:10,"show-count":""},null,8,["value"])]),_:1}),t(e(W),{label:"导入来源",path:"source"},{default:s(()=>[t(e(ce),{value:S.source,"onUpdate:value":n[1]||(n[1]=l=>S.source=l),placeholder:"例如：客户提供、爬虫获取等"},null,8,["value"])]),_:1}),t(e(W),{label:"标签"},{default:s(()=>[t(e(tt),{value:S.tags,"onUpdate:value":n[2]||(n[2]=l=>S.tags=l)},null,8,["value"])]),_:1}),t(e(W),{label:"描述"},{default:s(()=>[t(e(ce),{value:S.description,"onUpdate:value":n[3]||(n[3]=l=>S.description=l),placeholder:"可选，描述本次导入的目的或备注"},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(xe),{checked:S.autoVerify,"onUpdate:checked":n[4]||(n[4]=l=>S.autoVerify=l)},{default:s(()=>n[12]||(n[12]=[h(" 导入后自动验证邮箱 ")])),_:1,__:[12]},8,["checked"])]),_:1})]),_:1},8,["model"]),o("div",ao,[t(e(H),{justify:"end"},{default:s(()=>[t(e(O),{onClick:n[5]||(n[5]=l=>E.value=!1)},{default:s(()=>n[13]||(n[13]=[h("取消")])),_:1,__:[13]}),t(e(O),{type:"primary",onClick:j,loading:c.value},{default:s(()=>n[14]||(n[14]=[h(" 解析预览 ")])),_:1,__:[14]},8,["loading"])]),_:1})])])):A("",!0),b.value===2?(d(),_("div",ro,[t(e(we),{type:f.value.summary.invalid>0?"warning":"success",class:"mb-4"},{header:s(()=>n[15]||(n[15]=[h("解析结果")])),default:s(()=>[o("div",null,[o("p",null,"总计："+T(f.value.summary.total)+" 行",1),o("p",null,"有效："+T(f.value.summary.valid)+" 个账户",1),f.value.summary.invalid>0?(d(),_("p",io," 无效："+T(f.value.summary.invalid)+" 行（格式错误） ",1)):A("",!0)])]),_:1},8,["type"]),f.value.valid.length>0?(d(),_("div",uo,[n[16]||(n[16]=o("h4",null,"有效账户预览（前10个）：",-1)),t(e(de),{columns:R,data:f.value.valid.slice(0,10),pagination:!1,size:"small"},null,8,["data"]),f.value.valid.length>10?(d(),_("p",co," 还有 "+T(f.value.valid.length-10)+" 个账户未显示... ",1)):A("",!0)])):A("",!0),f.value.invalid.length>0?(d(),_("div",po,[n[17]||(n[17]=o("h4",{class:"text-red-500"},"无效数据：",-1)),t(e(st),{style:{"max-height":"200px"}},{default:s(()=>[(d(!0),_($e,null,Re(f.value.invalid,(l,C)=>(d(),_("div",{key:C,class:"text-red-500 text-sm"},T(l),1))),128))]),_:1})])):A("",!0),o("div",vo,[t(e(H),{justify:"end"},{default:s(()=>[t(e(O),{onClick:n[6]||(n[6]=l=>b.value=1)},{default:s(()=>n[18]||(n[18]=[h("返回修改")])),_:1,__:[18]}),t(e(O),{type:"primary",onClick:m,disabled:f.value.valid.length===0,loading:p.value},{default:s(()=>[h(" 确认导入 ("+T(f.value.valid.length)+" 个账户) ",1)]),_:1},8,["disabled","loading"])]),_:1})])])):A("",!0),b.value===3?(d(),_("div",fo,[L.value?(d(),_("div",_o,[t(e(ot),{status:L.value.success?"success":"error",title:L.value.success?"导入成功":"导入失败",description:L.value.message},{footer:s(()=>[L.value.success&&L.value.data?(d(),_("div",go,[o("p",null,"操作ID："+T(L.value.data.operation_id),1),o("p",null,"总数量："+T(L.value.data.total_count),1),o("p",null,"状态："+T(L.value.data.status),1),t(e(H),{justify:"center",class:"mt-4"},{default:s(()=>[t(e(O),{onClick:I},{default:s(()=>n[19]||(n[19]=[h("查看进度")])),_:1,__:[19]}),t(e(O),{type:"primary",onClick:G},{default:s(()=>n[20]||(n[20]=[h("完成")])),_:1,__:[20]})]),_:1})])):(d(),_("div",mo,[t(e(H),{justify:"center",class:"mt-4"},{default:s(()=>[t(e(O),{onClick:n[7]||(n[7]=l=>b.value=1)},{default:s(()=>n[21]||(n[21]=[h("重新导入")])),_:1,__:[21]}),t(e(O),{onClick:n[8]||(n[8]=l=>E.value=!1)},{default:s(()=>n[22]||(n[22]=[h("关闭")])),_:1,__:[22]})]),_:1})]))]),_:1},8,["status","title","description"])])):A("",!0)])):A("",!0)])]),_:1},8,["show"]))}}),yo=re(ko,[["__scopeId","data-v-bcaf745d"]]),ho={class:"text-gray-500 text-sm"},wo={class:"space-y-2"},xo={class:"flex items-center"},bo={class:"ml-2 font-medium"},Co={key:0,class:"text-sm text-gray-600 space-y-1"},$o={key:0},So={key:1},Mo={key:2},To={key:3},Po={key:1,class:"text-sm text-red-600"},Oo=V({__name:"ProxyConfigModal",props:{visible:{type:Boolean}},emits:["update:visible","success"],setup(u,{emit:v}){const x=u,P=v,$=z(!1),b=z(),r=ie(),c=oe({enabled:!1,proxy_url:"",proxy_type:"http"}),p=z(null),S=z(!1),f=z(!1),L=z(!1),E=z(!1),N=[{label:"HTTP/HTTPS",value:"http"},{label:"SOCKS5",value:"socks5"}],R={proxy_url:[{required:!0,trigger:["blur","input"],validator:(g,a)=>c.enabled?a?/^(https?|socks5):\/\/.+/.test(a)?!0:new Error("代理地址格式错误，应为: protocol://[username:password@]host:port"):new Error("请输入代理地址"):!0}]};ae(()=>x.visible,g=>{$.value=g,g&&(j(),p.value=null)}),ae($,g=>{P("update:visible",g)}),ae(()=>c.enabled,()=>{p.value=null}),ae(()=>c.proxy_url,()=>{p.value=null});const j=async()=>{try{console.log("[ProxyConfigModal] 开始加载代理配置...");const g=await U.getProxyConfig();console.log("[ProxyConfigModal] API响应:",g),console.log("[ProxyConfigModal] response.data:",g.data);let a=g.data;g.data&&typeof g.data.data<"u"?(a=g.data.data,console.log("[ProxyConfigModal] 检测到嵌套结构，使用response.data.data:",a)):console.log("[ProxyConfigModal] 使用直接结构，response.data:",a),a&&typeof a=="object"?(console.log("[ProxyConfigModal] 更新前的formData:",{...c}),c.enabled=!!a.enabled,c.proxy_url=String(a.proxy_url||""),c.proxy_type=String(a.proxy_type||"http"),console.log("[ProxyConfigModal] 更新后的formData:",{...c}),E.value=!!a.enabled||!!a.proxy_url):console.warn("[ProxyConfigModal] 未找到有效的代理配置数据，proxyData:",a)}catch(g){console.error("加载代理配置失败:",g),r.error("加载代理配置失败")}},m=async()=>{console.log("[ProxyConfigModal] 开始测试连接..."),S.value=!0,p.value=null;try{console.log("[ProxyConfigModal] 发送测试请求，配置:",c);const g=await U.testProxyConfig(c);console.log("[ProxyConfigModal] 测试响应原始数据:",g),console.log("[ProxyConfigModal] response.data:",g.data);let a=g.data;g.data&&typeof g.data.data<"u"?(a=g.data.data,console.log("[ProxyConfigModal] 使用嵌套结构 response.data.data:",a)):console.log("[ProxyConfigModal] 使用直接结构 response.data:",a),a?(console.log("[ProxyConfigModal] 设置测试结果:",a),console.log("[ProxyConfigModal] 测试结果详情:",{success:a.success,ip:a.ip,country:a.country,city:a.city,region:a.region,isp:a.isp,latency_ms:a.latency_ms,error:a.error}),p.value=a,a.success?r.success("代理连接测试成功"):r.warning("代理连接测试失败")):console.warn("[ProxyConfigModal] 测试数据为空")}catch(g){console.error("测试代理连接失败:",g),r.error("测试代理连接失败"),p.value={success:!1,error:"网络请求失败"}}finally{S.value=!1}},I=async()=>{if(b.value)try{await b.value.validate(),f.value=!0,await U.setProxyConfig(c),r.success("代理配置保存成功"),P("success"),$.value=!1}catch(g){console.error("保存代理配置失败:",g),g instanceof Error?r.error(`保存失败: ${g.message}`):r.error("保存代理配置失败")}finally{f.value=!1}},G=async()=>{L.value=!0;try{await U.deleteProxyConfig(),Object.assign(c,{enabled:!1,proxy_url:"",proxy_type:"http"}),p.value=null,E.value=!1,r.success("代理配置已重置"),P("success")}catch(g){console.error("重置代理配置失败:",g),r.error("重置代理配置失败")}finally{L.value=!1}};return(g,a)=>(d(),K(e(ge),{show:$.value,"onUpdate:show":a[4]||(a[4]=n=>$.value=n),preset:"dialog",title:"代理配置",style:{width:"600px"}},{action:s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{onClick:a[3]||(a[3]=n=>$.value=!1)},{default:s(()=>a[6]||(a[6]=[h("取消")])),_:1,__:[6]}),c.enabled||E.value?(d(),K(e(O),{key:0,type:"error",ghost:"",onClick:G,loading:L.value},{default:s(()=>a[7]||(a[7]=[h(" 重置 ")])),_:1,__:[7]},8,["loading"])):A("",!0),t(e(O),{type:"primary",onClick:I,loading:f.value},{default:s(()=>a[8]||(a[8]=[h(" 保存 ")])),_:1,__:[8]},8,["loading"])]),_:1})]),default:s(()=>[t(e(Ce),{ref_key:"formRef",ref:b,model:c,rules:R,"label-placement":"left","label-width":"120px"},{default:s(()=>[t(e(W),{label:"启用代理",path:"enabled"},{default:s(()=>[t(e(lt),{value:c.enabled,"onUpdate:value":a[0]||(a[0]=n=>c.enabled=n)},null,8,["value"]),a[5]||(a[5]=o("span",{class:"ml-2 text-gray-500"},"启用后所有网络请求将通过代理服务器",-1))]),_:1,__:[5]}),c.enabled?(d(),_($e,{key:0},[t(e(W),{label:"代理类型",path:"proxy_type"},{default:s(()=>[t(e(le),{value:c.proxy_type,"onUpdate:value":a[1]||(a[1]=n=>c.proxy_type=n),options:N,placeholder:"选择代理类型"},null,8,["value"])]),_:1}),t(e(W),{label:"代理地址",path:"proxy_url"},{default:s(()=>[t(e(ce),{value:c.proxy_url,"onUpdate:value":a[2]||(a[2]=n=>c.proxy_url=n),placeholder:"例如: http://username:<EMAIL>:8080",clearable:""},null,8,["value"])]),_:1})],64)):A("",!0),t(e(W),{label:"连接测试"},{default:s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{type:"primary",loading:S.value,onClick:m,disabled:!1},{default:s(()=>[h(T(c.enabled?"测试代理连接":"测试直连"),1)]),_:1},8,["loading"]),o("span",ho,T(c.enabled?"测试通过代理服务器的连接":"测试当前网络的直接连接"),1)]),_:1})]),_:1}),p.value?(d(),K(e(W),{key:1,label:"测试结果"},{default:s(()=>[t(e(ne),{size:"small",class:Ve(p.value.success?"border-green-200":"border-red-200")},{default:s(()=>[o("div",wo,[o("div",xo,[t(e(F),{color:p.value.success?"#10b981":"#ef4444",size:"16"},{default:s(()=>[p.value.success?(d(),K(e(Me),{key:0})):(d(),K(e(Te),{key:1}))]),_:1},8,["color"]),o("span",bo,T(p.value.success?"连接成功":"连接失败"),1)]),p.value.success?(d(),_("div",Co,[p.value.ip?(d(),_("div",$o,"IP地址: "+T(p.value.ip),1)):A("",!0),p.value.country?(d(),_("div",So," 位置: "+T(p.value.country)+" "+T(p.value.region)+" "+T(p.value.city),1)):A("",!0),p.value.isp?(d(),_("div",Mo,"ISP: "+T(p.value.isp),1)):A("",!0),p.value.latency_ms?(d(),_("div",To,"延迟: "+T(p.value.latency_ms)+"ms",1)):A("",!0)])):A("",!0),!p.value.success&&p.value.error?(d(),_("div",Po," 错误: "+T(p.value.error),1)):A("",!0)])]),_:1},8,["class"])]),_:1})):A("",!0)]),_:1},8,["model"])]),_:1},8,["show"]))}}),Lo=re(Oo,[["__scopeId","data-v-22196bc9"]]),zo={class:"verification-task-modal"},Io={class:"modal-actions"},Do=V({__name:"VerificationTaskModal",props:{visible:{type:Boolean},selectedAccounts:{}},emits:["update:visible","success"],setup(u,{emit:v}){const x=u,P=v,$=ie(),b=z(!1),r=oe({verificationType:"login",concurrentLimit:10,retryLimit:3,timeoutSeconds:30,skipRecentVerified:!0,updateAccountStatus:!0}),c=Z({get:()=>x.visible,set:j=>P("update:visible",j)}),p=Z(()=>x.selectedAccounts.length),S=Z(()=>{const j=p.value,m=r.concurrentLimit,I=r.timeoutSeconds+5;if(j===0)return"0分钟";const G=Math.ceil(j/m)*I,g=Math.ceil(G/60);if(g<60)return`约 ${g} 分钟`;{const a=Math.floor(g/60),n=g%60;return`约 ${a} 小时 ${n} 分钟`}}),f=[{label:"登录验证",value:"login"},{label:"健康检查",value:"health_check"},{label:"会话测试",value:"session_test"}],L={verificationType:{required:!0,message:"请选择验证类型",trigger:"change"},concurrentLimit:{required:!0,type:"number",min:1,max:50,message:"并发限制必须在1-50之间",trigger:"blur"},retryLimit:{required:!0,type:"number",min:0,max:10,message:"重试次数必须在0-10之间",trigger:"blur"}},E=j=>{const m=f.find(I=>I.value===j);return(m==null?void 0:m.label)||j},N=async()=>{if(p.value===0){$.error("请先选择要验证的账户");return}b.value=!0;try{const m={account_emails:x.selectedAccounts.map(g=>g.email),verification_type:r.verificationType,concurrent_limit:r.concurrentLimit,retry_limit:r.retryLimit},I=await U.startVerificationTask(m);console.log("验证任务响应:",JSON.stringify(I));const G=I.data||I;G.success?($.success("验证任务已创建，正在后台执行"),P("success",G.data),c.value=!1):$.error(G.message||"创建验证任务失败")}catch(j){$.error(j.message||"创建验证任务失败"),console.error(j)}finally{b.value=!1}},R=()=>{r.verificationType="login",r.concurrentLimit=10,r.retryLimit=3,r.timeoutSeconds=30,r.skipRecentVerified=!0,r.updateAccountStatus=!0};return ae(c,j=>{j||R()}),(j,m)=>(d(),K(e(ge),{show:c.value,"onUpdate:show":m[7]||(m[7]=I=>c.value=I),preset:"dialog",title:"启动验证任务",style:{width:"600px"}},{default:s(()=>[o("div",zo,[t(e(we),{type:"info",class:"mb-4"},{header:s(()=>m[8]||(m[8]=[h("验证任务说明")])),default:s(()=>[m[9]||(m[9]=o("div",null,[o("p",null,"将对选中的邮箱账户进行登录验证，检查账户的有效性。"),o("p",null,"验证过程将在后台异步执行，您可以在任务控制面板中查看进度。")],-1))]),_:1,__:[9]}),t(e(Ce),{ref:"formRef",model:r,rules:L,"label-placement":"left","label-width":"120px"},{default:s(()=>[t(e(W),{label:"选中账户数"},{default:s(()=>[t(e(se),{type:"info"},{default:s(()=>[h(T(p.value)+" 个账户",1)]),_:1})]),_:1}),t(e(W),{label:"验证类型",path:"verificationType"},{default:s(()=>[t(e(le),{value:r.verificationType,"onUpdate:value":m[0]||(m[0]=I=>r.verificationType=I),options:f,placeholder:"请选择验证类型"},null,8,["value"])]),_:1}),t(e(W),{label:"并发限制",path:"concurrentLimit"},{feedback:s(()=>m[10]||(m[10]=[o("span",{class:"text-gray-500"},"建议设置为 5-20，过高可能导致IP被封",-1)])),default:s(()=>[t(e(he),{value:r.concurrentLimit,"onUpdate:value":m[1]||(m[1]=I=>r.concurrentLimit=I),min:1,max:50,placeholder:"同时验证的账户数量"},null,8,["value"])]),_:1}),t(e(W),{label:"重试次数",path:"retryLimit"},{default:s(()=>[t(e(he),{value:r.retryLimit,"onUpdate:value":m[2]||(m[2]=I=>r.retryLimit=I),min:0,max:10,placeholder:"验证失败时的重试次数"},null,8,["value"])]),_:1}),t(e(W),{label:"超时设置"},{default:s(()=>[t(e(he),{value:r.timeoutSeconds,"onUpdate:value":m[3]||(m[3]=I=>r.timeoutSeconds=I),min:10,max:300,placeholder:"单个账户验证超时时间（秒）"},null,8,["value"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(xe),{checked:r.skipRecentVerified,"onUpdate:checked":m[4]||(m[4]=I=>r.skipRecentVerified=I)},{default:s(()=>m[11]||(m[11]=[h(" 跳过最近24小时内已验证的账户 ")])),_:1,__:[11]},8,["checked"])]),_:1}),t(e(W),null,{default:s(()=>[t(e(xe),{checked:r.updateAccountStatus,"onUpdate:checked":m[5]||(m[5]=I=>r.updateAccountStatus=I)},{default:s(()=>m[12]||(m[12]=[h(" 根据验证结果自动更新账户状态 ")])),_:1,__:[12]},8,["checked"])]),_:1})]),_:1},8,["model"]),t(e(ne),{title:"预估信息",size:"small",class:"mb-4"},{default:s(()=>[t(e(nt),{column:2,size:"small"},{default:s(()=>[t(e(fe),{label:"预计耗时"},{default:s(()=>[h(T(S.value),1)]),_:1}),t(e(fe),{label:"并发数"},{default:s(()=>[h(T(r.concurrentLimit),1)]),_:1}),t(e(fe),{label:"总账户数"},{default:s(()=>[h(T(p.value),1)]),_:1}),t(e(fe),{label:"验证类型"},{default:s(()=>[h(T(E(r.verificationType)),1)]),_:1})]),_:1})]),_:1}),o("div",Io,[t(e(H),{justify:"end"},{default:s(()=>[t(e(O),{onClick:m[6]||(m[6]=I=>c.value=!1)},{default:s(()=>m[13]||(m[13]=[h("取消")])),_:1,__:[13]}),t(e(O),{type:"primary",onClick:N,loading:b.value,disabled:p.value===0},{default:s(()=>m[14]||(m[14]=[h(" 启动验证任务 ")])),_:1,__:[14]},8,["loading","disabled"])]),_:1})])])]),_:1},8,["show"]))}}),jo=re(Do,[["__scopeId","data-v-c84d343d"]]),Bo={class:"task-control-panel"},No=V({__name:"TaskControlPanel",props:{visible:{type:Boolean}},emits:["update:visible","refresh"],setup(u,{emit:v}){const x=u,P=v,$=ie(),b=at(),r=z(!1),c=z(!1),p=z([]),S=z([]),f=Z({get:()=>x.visible,set:l=>P("update:visible",l)}),L=Z(()=>{const l={running:0,stopped:0,paused:0,error:0};return p.value.forEach(C=>{switch(C.status){case"running":l.running++;break;case"stopped":l.stopped++;break;case"paused":l.paused++;break;case"error":l.error++;break}}),l}),E=oe({page:1,pageSize:10,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50]}),N=Z(()=>[{title:"任务名称",key:"task_name",width:200,ellipsis:{tooltip:!0}},{title:"任务类型",key:"task_type",width:120,render:l=>({batch_verification:"批量验证",health_check:"健康检查",cleanup:"清理任务"})[l.task_type]||l.task_type},{title:"状态",key:"status",width:100,render:l=>{const q={running:{text:"运行中",type:"success"},stopped:{text:"已停止",type:"info"},paused:{text:"已暂停",type:"warning"},error:{text:"错误",type:"error"}}[l.status]||{text:l.status,type:"info"};return D(se,{type:q.type},{default:()=>q.text})}},{title:"运行次数",key:"run_count",width:100},{title:"错误次数",key:"error_count",width:100,render:l=>l.error_count>0?D(se,{type:"error"},{default:()=>l.error_count}):l.error_count},{title:"最后运行",key:"last_run_at",width:150,render:l=>l.last_run_at?new Date(l.last_run_at).toLocaleString():"-"},{title:"操作",key:"actions",width:200,render:l=>D(H,[D(O,{size:"small",type:l.status==="running"?"warning":"primary",onClick:()=>I(l)},{default:()=>l.status==="running"?"暂停":"启动",icon:()=>D(F,null,{default:()=>l.status==="running"?D(fs):D(Ie)})}),D(O,{size:"small",type:"error",onClick:()=>G(l),disabled:l.status==="stopped"},{default:()=>"停止",icon:()=>D(F,null,{default:()=>D(je)})}),D(O,{size:"small",onClick:()=>g(l)},{default:()=>"重置"})])}]),R=Z(()=>[{title:"操作ID",key:"operation_id",width:120,ellipsis:{tooltip:!0}},{title:"操作类型",key:"operation_type",width:100,render:l=>({import:"批量导入",verify:"批量验证",disable:"批量禁用",enable:"批量启用"})[l.operation_type]||l.operation_type},{title:"状态",key:"status",width:100,render:l=>{const q={pending:{text:"等待中",type:"info"},running:{text:"运行中",type:"warning"},completed:{text:"已完成",type:"success"},failed:{text:"失败",type:"error"},cancelled:{text:"已取消",type:"info"}}[l.status]||{text:l.status,type:"info"};return D(se,{type:q.type},{default:()=>q.text})}},{title:"进度",key:"progress",width:150,render:l=>{const C=l.total_count>0?Math.round(l.processed_count/l.total_count*100):0;return D("div",[D(rt,{type:"line",percentage:C,showIndicator:!1,height:8}),D("div",{class:"text-xs text-gray-500 mt-1"},`${l.processed_count}/${l.total_count}`)])}},{title:"成功/失败",key:"result",width:100,render:l=>D("div",{class:"text-xs"},[D("div",{class:"text-green-600"},`成功: ${l.success_count}`),D("div",{class:"text-red-600"},`失败: ${l.failed_count}`)])},{title:"创建时间",key:"created_at",width:150,render:l=>new Date(l.created_at).toLocaleString()}]),j=async()=>{r.value=!0;try{const l=await U.getTaskSchedulerStatus();l.success&&l.data&&(p.value=l.data)}catch(l){$.error("加载任务状态失败"),console.error(l)}finally{r.value=!1}},m=async()=>{c.value=!0,c.value=!1},I=async l=>{try{const C=l.status==="running"?"pause":"start";await U.controlTask({action:C,task_id:l.task_name}),$.success(`任务${C==="start"?"启动":"暂停"}成功`),j(),P("refresh")}catch(C){$.error(C.message||"操作失败")}},G=async l=>{try{await U.controlTask({action:"stop",task_id:l.task_name}),$.success("任务停止成功"),j(),P("refresh")}catch(C){$.error(C.message||"停止任务失败")}},g=async l=>{b.warning({title:"确认重置",content:`确定要重置任务 "${l.task_name}" 吗？这将清除运行计数和错误记录。`,positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{try{await U.controlTask({action:"reset",task_id:l.task_name}),$.success("任务重置成功"),j(),P("refresh")}catch(C){$.error(C.message||"重置任务失败")}}})},a=async()=>{b.info({title:"确认启动",content:"确定要启动所有已停止的任务吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{const l=p.value.filter(C=>C.status==="stopped");for(const C of l)try{await U.controlTask({action:"start",task_id:C.task_name})}catch(q){console.error(`启动任务 ${C.task_name} 失败:`,q)}$.success("批量启动完成"),j(),P("refresh")}})},n=async()=>{b.warning({title:"确认停止",content:"确定要停止所有运行中的任务吗？",positiveText:"确认",negativeText:"取消",onPositiveClick:async()=>{const l=p.value.filter(C=>C.status==="running");for(const C of l)try{await U.controlTask({action:"stop",task_id:C.task_name})}catch(q){console.error(`停止任务 ${C.task_name} 失败:`,q)}$.success("批量停止完成"),j(),P("refresh")}})};return Se(()=>{j(),m()}),(l,C)=>(d(),K(e(it),{show:f.value,"onUpdate:show":C[0]||(C[0]=q=>f.value=q),width:800,placement:"right"},{default:s(()=>[t(e(ut),{title:"定时任务控制面板"},{default:s(()=>[o("div",Bo,[t(e(ne),{title:"任务状态概览",class:"mb-4"},{default:s(()=>[t(e(Ue),{cols:4,"x-gap":12},{default:s(()=>[t(e(X),null,{default:s(()=>[t(e(Y),{label:"运行中",value:L.value.running},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已停止",value:L.value.stopped},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已暂停",value:L.value.paused},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"错误",value:L.value.error},null,8,["value"])]),_:1})]),_:1})]),_:1}),t(e(ne),{title:"任务列表"},{"header-extra":s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{onClick:j,loading:r.value},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[C[1]||(C[1]=h(" 刷新 "))]),_:1,__:[1]},8,["loading"]),t(e(O),{type:"primary",onClick:a},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Ie))]),_:1})]),default:s(()=>[C[2]||(C[2]=h(" 全部启动 "))]),_:1,__:[2]}),t(e(O),{onClick:n},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(je))]),_:1})]),default:s(()=>[C[3]||(C[3]=h(" 全部停止 "))]),_:1,__:[3]})]),_:1})]),default:s(()=>[t(e(de),{columns:N.value,data:p.value,loading:r.value,pagination:!1,"row-key":q=>q.id},null,8,["columns","data","loading","row-key"])]),_:1}),t(e(ne),{title:"批量操作历史",class:"mt-4"},{"header-extra":s(()=>[t(e(O),{onClick:m,loading:c.value},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[C[4]||(C[4]=h(" 刷新 "))]),_:1,__:[4]},8,["loading"])]),default:s(()=>[t(e(de),{columns:R.value,data:S.value,loading:c.value,pagination:E,"row-key":q=>q.id},null,8,["columns","data","loading","pagination","row-key"])]),_:1})])]),_:1})]),_:1},8,["show"]))}}),Uo=re(No,[["__scopeId","data-v-252d8a66"]]),Ne={getTaskLogs(u){return J.get("/mailbox/task-logs",{params:u})},getTaskLogDetail(u){return J.get(`/mailbox/task-logs/${u}/details`)},createTaskLog(u){return J.post("/mailbox/task-logs",u)},updateTaskLog(u,v){return J.put(`/mailbox/task-logs/${u}`,v)}},Ao={class:"filter-section mb-4"},Ro={key:0,class:"detail-log-container"},Vo={class:"log-header mb-4"},Eo={class:"text-gray-600"},Fo={class:"text-gray-500"},qo={class:"step-content"},Jo={class:"mb-2"},Ho={key:0,class:"text-sm text-gray-500 mb-2"},Wo={key:1,class:"text-red-500 mb-2"},Go={key:2,class:"response-data"},Ko={key:1,class:"no-steps"},Qo={key:0,class:"mt-4"},Xo=V({__name:"TaskLogPanel",setup(u){const v=ie(),x=z(!1),P=z([]),$=z(!1),b=z(!1),r=z(null),c=oe({dateRange:[Date.now()-4320*60*1e3,Date.now()],operationType:null,status:null,email:""}),p=oe({page:1,pageSize:20,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50,100]}),S=[{label:"批量导入",value:"import"},{label:"邮箱验证",value:"verify"}],f=[{label:"成功",value:"success"},{label:"失败",value:"failed"},{label:"进行中",value:"running"},{label:"等待中",value:"pending"}],L=k=>({success:{type:"success",text:"成功",icon:Me},failed:{type:"error",text:"失败",icon:Te},running:{type:"info",text:"进行中",icon:Be},pending:{type:"warning",text:"等待中",icon:Be}})[k]||{type:"default",text:k,icon:be},E=k=>({import:{text:"批量导入",icon:Ee,color:"#2080f0"},verify:{text:"邮箱验证",icon:Pt,color:"#18a058"}})[k]||{text:k,icon:be,color:"#666"},N=Z(()=>[{title:"时间",key:"start_time",width:160,render:k=>new Date(k.start_time).toLocaleString()},{title:"操作类型",key:"operation_type",width:120,render:k=>{const w=E(k.operation_type);return D(H,{align:"center"},{default:()=>[D(F,{color:w.color},{default:()=>D(w.icon)}),D("span",w.text)]})}},{title:"邮箱地址",key:"email",width:200,ellipsis:{tooltip:!0}},{title:"状态",key:"status",width:100,render:k=>{const w=L(k.status);return D(H,{align:"center"},{default:()=>[D(F,{},{default:()=>D(w.icon)}),D(se,{type:w.type},{default:()=>w.text})]})}},{title:"耗时",key:"duration_ms",width:100,render:k=>k.duration_ms?k.duration_ms<1e3?`${k.duration_ms}ms`:`${(k.duration_ms/1e3).toFixed(1)}s`:"-"},{title:"详细日志",key:"detail_log",width:200,render:k=>D(O,{size:"small",type:"primary",text:!0,onClick:()=>a(k)},{default:()=>"查看详细日志"})},{title:"批次ID",key:"batch_id",width:120,render:k=>k.batch_id||"-"}]),R=async()=>{try{x.value=!0;const k={page:p.page,page_size:p.pageSize};c.operationType&&(k.operation_type=c.operationType),c.status&&(k.status=c.status),c.email&&(k.email=c.email),c.dateRange&&c.dateRange.length===2&&(k.start_time=new Date(c.dateRange[0]).toISOString(),k.end_time=new Date(c.dateRange[1]).toISOString());const w=await Ne.getTaskLogs(k),M=w.data||w;M.code===200&&M.data?(P.value=M.data.items||[],p.itemCount=M.data.total||0):(P.value=[],p.itemCount=0,M.message&&v.error("加载任务日志失败："+M.message))}catch(k){v.error("加载任务日志失败"),console.error(k),P.value=[],p.itemCount=0}finally{x.value=!1}},j=()=>{R()},m=()=>{p.page=1,R()},I=()=>{c.dateRange=[Date.now()-4320*60*1e3,Date.now()],c.operationType=null,c.status=null,c.email="",p.page=1,R()},G=k=>{p.page=k,R()},g=k=>{p.pageSize=k,p.page=1,R()},a=async k=>{$.value=!0,b.value=!0;try{const w=await Ne.getTaskLogDetail(k.id),M=w.data||w;if(M.code===200&&M.data){const ee=M.data;ee.task_log&&ee.detail?r.value={taskLog:ee.task_log,detail:ee.detail}:(v.error("详细日志数据格式不正确"),r.value=null)}else v.error("获取详细日志失败："+(M.message||"未知错误")),r.value=null}catch(w){v.error("获取详细日志失败"),console.error(w),r.value=null}finally{b.value=!1}},n=k=>new Date(k).toLocaleString(),l=k=>({success:"success",failed:"error",running:"info",pending:"warning"})[k]||"default",C=k=>({success:"成功",failed:"失败",running:"进行中",pending:"等待中"})[k]||k,q=k=>({success:"success",failed:"error",running:"info"})[k]||"default";return Se(()=>{R()}),(k,w)=>(d(),K(e(ne),{title:"任务日志",class:"task-log-panel"},{"header-extra":s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{onClick:j,size:"small"},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[w[6]||(w[6]=h(" 刷新 "))]),_:1,__:[6]})]),_:1})]),default:s(()=>[o("div",Ao,[t(e(H),null,{default:s(()=>[t(e(ct),{value:c.dateRange,"onUpdate:value":w[0]||(w[0]=M=>c.dateRange=M),type:"daterange",placeholder:"选择时间范围","default-value":[Date.now()-4320*60*1e3,Date.now()],style:{width:"240px"}},null,8,["value","default-value"]),t(e(le),{value:c.operationType,"onUpdate:value":w[1]||(w[1]=M=>c.operationType=M),options:S,placeholder:"操作类型",clearable:"",style:{width:"120px"}},null,8,["value"]),t(e(le),{value:c.status,"onUpdate:value":w[2]||(w[2]=M=>c.status=M),options:f,placeholder:"状态",clearable:"",style:{width:"120px"}},null,8,["value"]),t(e(ce),{value:c.email,"onUpdate:value":w[3]||(w[3]=M=>c.email=M),placeholder:"邮箱地址",clearable:"",style:{width:"200px"}},null,8,["value"]),t(e(O),{onClick:m,type:"primary",size:"small"},{default:s(()=>w[7]||(w[7]=[h("筛选")])),_:1,__:[7]}),t(e(O),{onClick:I,size:"small"},{default:s(()=>w[8]||(w[8]=[h("重置")])),_:1,__:[8]})]),_:1})]),t(e(de),{columns:N.value,data:P.value,loading:x.value,pagination:p,"row-key":M=>M.id,"onUpdate:page":G,"onUpdate:pageSize":g},null,8,["columns","data","loading","pagination","row-key"]),t(e(ge),{show:$.value,"onUpdate:show":w[5]||(w[5]=M=>$.value=M),preset:"card",title:"详细日志",style:{width:"80%","max-width":"1200px"},"mask-closable":!1},{"header-extra":s(()=>[t(e(O),{onClick:w[4]||(w[4]=M=>$.value=!1),quaternary:"",circle:""},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(At))]),_:1})]),_:1})]),default:s(()=>[t(e(dt),{show:b.value},{default:s(()=>[r.value&&r.value.taskLog?(d(),_("div",Ro,[o("div",Vo,[t(e(H),null,{default:s(()=>[t(e(se),{type:l(r.value.taskLog.status)},{default:s(()=>[h(T(C(r.value.taskLog.status)),1)]),_:1},8,["type"]),o("span",Eo,T(r.value.taskLog.email),1),o("span",Fo,T(n(r.value.taskLog.start_time)),1)]),_:1})]),r.value.detail&&r.value.detail.steps&&r.value.detail.steps.length>0?(d(),K(e(pt),{key:0},{default:s(()=>[(d(!0),_($e,null,Re(r.value.detail.steps,(M,ee)=>(d(),K(e(vt),{key:ee,type:q(M.status),title:M.step,time:n(M.timestamp)},{default:s(()=>[o("div",qo,[o("p",Jo,T(M.details),1),M.duration?(d(),_("div",Ho," 耗时: "+T(M.duration)+"ms ",1)):A("",!0),M.error_message?(d(),_("div",Wo," 错误: "+T(M.error_message),1)):A("",!0),M.response_data&&Object.keys(M.response_data).length>0?(d(),_("div",Go,[t(e(ze),{code:JSON.stringify(M.response_data,null,2),language:"json","show-line-numbers":""},null,8,["code"])])):A("",!0)])]),_:2},1032,["type","title","time"]))),128))]),_:1})):(d(),_("div",Ko,[w[10]||(w[10]=o("p",{class:"text-gray-500"},"暂无详细执行步骤记录",-1)),r.value.taskLog&&r.value.taskLog.error_message?(d(),_("div",Qo,[w[9]||(w[9]=o("h4",{class:"mb-2"},"错误信息:",-1)),t(e(ze),{code:r.value.taskLog.error_message,language:"text"},null,8,["code"])])):A("",!0)]))])):A("",!0)]),_:1},8,["show"])]),_:1},8,["show"])]),_:1}))}}),Yo=re(Xo,[["__scopeId","data-v-9422c108"]]),Zo={class:"mailbox-management"},el={class:"proxy-status-tooltip"},tl={class:"status-line"},sl={key:0,class:"status-line"},ol={key:1,class:"status-line"},ll={key:2,class:"status-line"},nl={key:3,class:"status-line"},al={key:4,class:"status-line"},rl={class:"error-text"},il={class:"filter-section mb-4"},ul={class:"statistics-section mb-4"},cl={class:"batch-operations mb-4"},dl=V({__name:"MailboxManagement",setup(u){const v=ie(),x=z(!1),P=z([]),$=z([]),b=z([]),r=z(!1),c=z(!1),p=z(!1),S=z(!1),f=z({status:"loading",statusText:"检测中...",latency:null,proxyInfo:null,location:null,ip:null,error:null});let L=null;const E=z({id:0,stat_date:"",total_accounts:0,active_accounts:0,verified_accounts:0,failed_accounts:0,disabled_accounts:0,new_imports_today:0,verification_success_rate:0,avg_verification_time_ms:0,created_at:""}),N=oe({status:[],verification_status:[],import_source:[],tags:[]}),R=oe({page:1,pageSize:20,itemCount:0,showSizePicker:!0,pageSizes:[10,20,50,100]}),j=U.getStatusOptions(),m=U.getVerificationStatusOptions(),I=U.getImportSourceOptions(),G=Z(()=>[{type:"selection",multiple:!0},{title:"邮箱地址",key:"email",width:200,ellipsis:{tooltip:!0}},{title:"登录状态",key:"login_status",width:100,render:y=>{const i=U.formatStatus(y.login_status);return D(se,{type:i.type},{default:()=>i.text})}},{title:"验证状态",key:"verification_status",width:100,render:y=>{const i=U.formatStatus(y.verification_status);return D(se,{type:i.type},{default:()=>i.text})}},{title:"导入来源",key:"import_source",width:100},{title:"创建时间",key:"created_at",width:150,render:y=>new Date(y.created_at).toLocaleString()},{title:"最后验证",key:"last_verification_time",width:150,render:y=>y.last_verification_time?new Date(y.last_verification_time).toLocaleString():"-"},{title:"操作",key:"actions",width:150,render:y=>D(H,{},{default:()=>[D(O,{size:"small",type:y.is_disabled?"success":"warning",onClick:()=>Ge(y)},{default:()=>y.is_disabled?"启用":"禁用"}),D(O,{size:"small",type:"error",onClick:()=>We(y)},{default:()=>"删除"})]})}]),g=async()=>{x.value=!0;try{const y={page:R.page,page_size:R.pageSize,status:N.status.length>0?N.status:void 0,verification_status:N.verification_status.length>0?N.verification_status:void 0,import_source:N.import_source.length>0?N.import_source:void 0,tags:N.tags.length>0?N.tags:void 0},i=await U.getAccountsList(y);console.log("加载账户列表响应:",JSON.stringify(i));const B=i.data||i;B.success&&B.data?(P.value=B.data.Items||B.data.items||[],R.itemCount=B.data.Total||B.data.total||0):(P.value=[],R.itemCount=0)}catch(y){v.error("加载账户列表失败"),console.error(y),P.value=[],R.itemCount=0}finally{x.value=!1}},a=async()=>{try{const y=await U.getMailboxStatistics(),i=y.data||y;i.success&&i.data&&(E.value=i.data)}catch(y){console.error("加载统计信息失败:",y)}},n=()=>{g(),a()},l=()=>{R.page=1,g()},C=()=>{N.status=[],N.verification_status=[],N.import_source=[],N.tags=[],R.page=1,g()},q=y=>{R.page=y,g()},k=y=>{R.pageSize=y,R.page=1,g()},w=()=>{v.success("批量导入任务已创建"),n()},M=()=>{v.success("代理配置已保存"),me()},ee=()=>{v.success("验证任务已创建"),n()},Fe=y=>{b.value=y,$.value=(P.value||[]).filter(i=>y.includes(i.id))},qe=()=>{if($.value.length===0){v.warning("请先选择要验证的账户");return}p.value=!0},Je=async()=>{if(b.value.length===0){v.warning("请先选择要禁用的账户");return}try{await U.batchDisableAccounts(b.value),v.success(`成功禁用 ${b.value.length} 个账户`),b.value=[],$.value=[],n()}catch(y){v.error("批量禁用失败"),console.error(y)}},He=async()=>{if(b.value.length===0){v.warning("请先选择要删除的账户");return}try{await U.batchDeleteAccounts(b.value),v.success(`成功删除 ${b.value.length} 个账户`),b.value=[],$.value=[],n()}catch(y){v.error("批量删除失败"),console.error(y)}},We=async y=>{try{await U.deleteAccount(y.id),v.success(`成功删除账户 ${y.email}`),n()}catch(i){v.error(`删除账户 ${y.email} 失败`),console.error(i)}},Ge=async y=>{try{const i=!y.is_disabled;await U.toggleAccountStatus(y.id,i),v.success(`成功${i?"禁用":"启用"}账户 ${y.email}`),n()}catch(i){v.error(`${y.is_disabled?"启用":"禁用"}账户失败`),console.error(i)}},Ke=()=>{a()},me=async()=>{var y,i;try{f.value={status:"loading",statusText:"检测中...",latency:null,proxyInfo:null,location:null,ip:null,error:null};const B=await U.getProxyConfig(),te=((y=B.data)==null?void 0:y.data)||B.data;if(!te){f.value={status:"disabled",statusText:"未启用",latency:null,proxyInfo:null,location:null,ip:null,error:null};return}const Pe=await U.testProxyConfig(te),Q=((i=Pe.data)==null?void 0:i.data)||Pe.data;if(Q){const pe=Q.success,Oe=Q.latency_ms||-1;let ke=null;if(te.enabled&&te.proxy_url)try{const Le=new URL(te.proxy_url),ue=Le.hostname,Ye=Le.port,Ze=ue.length>6?`${ue.substring(0,3)}***${ue.substring(ue.length-3)}`:`${ue.substring(0,1)}***`;ke=`${te.proxy_type.toUpperCase()} ${Ze}:${Ye}`}catch{ke=`${te.proxy_type.toUpperCase()} 代理`}let ve=null;Q.country&&(ve=`${Q.country}`,Q.region&&(ve+=` ${Q.region}`),Q.city&&(ve+=` ${Q.city}`)),f.value={status:pe&&Oe<5e3?"success":"error",statusText:te.enabled?pe?"已启用":"连接异常":"未启用",latency:pe?Oe:-1,proxyInfo:ke,location:ve,ip:Q.ip,error:pe?null:Q.error}}else f.value={status:"error",statusText:"检测失败",latency:null,proxyInfo:null,location:null,ip:null,error:"无法获取测试结果"}}catch(B){console.error("代理状态检测失败:",B),f.value={status:"disabled",statusText:"检测失败",latency:null,proxyInfo:null,location:null,ip:null,error:"网络请求失败"}}},Qe=()=>{L&&clearInterval(L),L=setInterval(()=>{f.value.status!=="disabled"&&me()},36e4)},Xe=()=>{L&&(clearInterval(L),L=null)};return Se(()=>{n(),me(),Qe()}),_t(()=>{Xe()}),(y,i)=>(d(),_("div",Zo,[t(e(ne),{title:"邮箱管理",class:"mb-4"},{"header-extra":s(()=>[t(e(H),null,{default:s(()=>[t(e(O),{type:"primary",onClick:i[0]||(i[0]=B=>r.value=!0)},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Ee))]),_:1})]),default:s(()=>[i[9]||(i[9]=h(" 批量导入 "))]),_:1,__:[9]}),t(e(ft),{trigger:"hover",placement:"bottom","show-arrow":!0,class:"proxy-status-popover-wrapper"},{trigger:s(()=>[t(e(O),{onClick:i[1]||(i[1]=B=>c.value=!0),class:"proxy-config-button"},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(De))]),_:1})]),default:s(()=>[i[10]||(i[10]=h(" 代理配置 ")),o("div",{class:Ve(["proxy-status-indicator",{"status-success":f.value.status==="success","status-error":f.value.status==="error","status-loading":f.value.status==="loading","status-disabled":f.value.status==="disabled"}])},[t(e(F),{size:"12"},{default:s(()=>[f.value.status==="success"?(d(),K(e(oo),{key:0})):f.value.status==="error"?(d(),K(e(Te),{key:1})):f.value.status==="loading"?(d(),K(e(Bs),{key:2})):(d(),K(e(Gt),{key:3}))]),_:1})],2)]),_:1,__:[10]})]),default:s(()=>[o("div",el,[o("div",tl,[t(e(F),{size:"14",class:"status-icon status-icon-server"},{default:s(()=>[t(e(De))]),_:1}),i[11]||(i[11]=o("strong",null,"代理状态：",-1)),o("span",null,T(f.value.statusText),1)]),f.value.latency!==null?(d(),_("div",sl,[t(e(F),{size:"14",class:"status-icon status-icon-speed"},{default:s(()=>[t(e(gt))]),_:1}),i[12]||(i[12]=o("strong",null,"连接延迟：",-1)),o("span",null,T(f.value.latency===-1?"连接失败":`${f.value.latency}ms`),1)])):A("",!0),f.value.proxyInfo?(d(),_("div",ol,[t(e(F),{size:"14",class:"status-icon status-icon-info"},{default:s(()=>[t(e(be))]),_:1}),i[13]||(i[13]=o("strong",null,"代理信息：",-1)),o("span",null,T(f.value.proxyInfo),1)])):A("",!0),f.value.ip?(d(),_("div",ll,[t(e(F),{size:"14",class:"status-icon status-icon-ip"},{default:s(()=>[t(e(Yt))]),_:1}),i[14]||(i[14]=o("strong",null,"IP地址：",-1)),o("span",null,T(f.value.ip),1)])):A("",!0),f.value.location?(d(),_("div",nl,[t(e(F),{size:"14",class:"status-icon status-icon-location"},{default:s(()=>[t(e(us))]),_:1}),i[15]||(i[15]=o("strong",null,"IP位置：",-1)),o("span",null,T(f.value.location),1)])):A("",!0),f.value.error?(d(),_("div",al,[t(e(F),{size:"14",class:"status-icon status-icon-error"},{default:s(()=>[t(e(Qs))]),_:1}),i[16]||(i[16]=o("strong",null,"错误信息：",-1)),o("span",rl,T(f.value.error),1)])):A("",!0)])]),_:1}),t(e(O),{onClick:n},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(_e))]),_:1})]),default:s(()=>[i[17]||(i[17]=h(" 刷新 "))]),_:1,__:[17]})]),_:1})]),default:s(()=>[o("div",il,[t(e(H),null,{default:s(()=>[t(e(le),{value:N.status,"onUpdate:value":i[2]||(i[2]=B=>N.status=B),options:e(j),placeholder:"登录状态",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(le),{value:N.verification_status,"onUpdate:value":i[3]||(i[3]=B=>N.verification_status=B),options:e(m),placeholder:"验证状态",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(le),{value:N.import_source,"onUpdate:value":i[4]||(i[4]=B=>N.import_source=B),options:e(I),placeholder:"导入来源",clearable:"",multiple:"",style:{width:"150px"}},null,8,["value","options"]),t(e(O),{onClick:l,type:"primary"},{default:s(()=>i[18]||(i[18]=[h("筛选")])),_:1,__:[18]}),t(e(O),{onClick:C},{default:s(()=>i[19]||(i[19]=[h("重置")])),_:1,__:[19]})]),_:1})]),o("div",ul,[t(e(Ue),{cols:6,"x-gap":12},{default:s(()=>[t(e(X),null,{default:s(()=>[t(e(Y),{label:"总账户数",value:E.value.total_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"活跃账户",value:E.value.active_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已验证",value:E.value.verified_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"验证失败",value:E.value.failed_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"已禁用",value:E.value.disabled_accounts},null,8,["value"])]),_:1}),t(e(X),null,{default:s(()=>[t(e(Y),{label:"验证成功率",value:`${E.value.verification_success_rate.toFixed(1)}%`},null,8,["value"])]),_:1})]),_:1})]),o("div",cl,[t(e(H),null,{default:s(()=>[t(e(O),{type:"primary",disabled:b.value.length===0,onClick:qe},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(Me))]),_:1})]),default:s(()=>[h(" 批量验证 ("+T(b.value.length)+") ",1)]),_:1},8,["disabled"]),t(e(O),{type:"warning",disabled:b.value.length===0,onClick:Je},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(wt))]),_:1})]),default:s(()=>[i[20]||(i[20]=h(" 批量禁用 "))]),_:1,__:[20]},8,["disabled"]),t(e(O),{type:"error",disabled:b.value.length===0,onClick:He},{icon:s(()=>[t(e(F),null,{default:s(()=>[t(e(qs))]),_:1})]),default:s(()=>[i[21]||(i[21]=h(" 批量删除 "))]),_:1,__:[21]},8,["disabled"])]),_:1})]),t(e(de),{columns:G.value,data:P.value,loading:x.value,pagination:R,"row-key":B=>B.id,"checked-row-keys":b.value,"onUpdate:checkedRowKeys":Fe,"onUpdate:page":q,"onUpdate:pageSize":k},null,8,["columns","data","loading","pagination","row-key","checked-row-keys"])]),_:1}),t(Uo,{visible:S.value,"onUpdate:visible":i[5]||(i[5]=B=>S.value=B),onRefresh:Ke},null,8,["visible"]),t(yo,{visible:r.value,"onUpdate:visible":i[6]||(i[6]=B=>r.value=B),onSuccess:w},null,8,["visible"]),t(Lo,{visible:c.value,"onUpdate:visible":i[7]||(i[7]=B=>c.value=B),onSuccess:M},null,8,["visible"]),t(jo,{visible:p.value,"onUpdate:visible":i[8]||(i[8]=B=>p.value=B),"selected-accounts":$.value,onSuccess:ee},null,8,["visible","selected-accounts"]),t(Yo,{class:"mt-4"})]))}}),gl=re(dl,[["__scopeId","data-v-075867ef"]]);export{gl as default};
